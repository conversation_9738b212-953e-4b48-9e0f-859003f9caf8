import React, { useState, useRef, useEffect } from 'react';
import { Bug, Bot, Plus, X } from 'lucide-react';
import { BugReportModal } from './BugReportModal';
import { AIAssistantModal } from './AIAssistantModal';
import { getDevModeStatus, getFloatingButtonStatus } from '../utils/api/bugReportApi';
import './FloatingActionButton.css';

interface FloatingActionButtonProps {
  className?: string;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showBugReportModal, setShowBugReportModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [isTestMode, setIsTestMode] = useState(false);
  const [isFloatingButtonEnabled, setIsFloatingButtonEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isHidden, setIsHidden] = useState(false);
  const [position, setPosition] = useState({ x: 24, y: 24 }); // 距離右下角的距離
  const [isDragging, setIsDragging] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const fabRef = useRef<HTMLDivElement>(null);
  const dragRef = useRef({ startX: 0, startY: 0, startPosX: 0, startPosY: 0 });

  // 檢查測試模式、懸浮球啟用狀態和設備類型
  useEffect(() => {
    const checkStatus = async () => {
      try {
        // 並行檢查測試模式和懸浮球啟用狀態
        const [testModeEnabled, floatingButtonEnabled] = await Promise.all([
          getDevModeStatus(),
          getFloatingButtonStatus()
        ]);

        setIsTestMode(testModeEnabled);
        setIsFloatingButtonEnabled(floatingButtonEnabled);
      } catch (error) {
        console.error('檢查狀態失敗:', error);
        setIsTestMode(false);
        setIsFloatingButtonEnabled(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 檢查是否為移動設備
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkStatus();
    checkMobile();

    // 監聽窗口大小變化
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 智能隱藏功能 - 滾動時自動隱藏
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // 向下滾動且滾動距離超過100px時隱藏
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsHidden(true);
        setIsExpanded(false); // 隱藏時也收起展開狀態
      }
      // 向上滾動時顯示
      else if (currentScrollY < lastScrollY) {
        setIsHidden(false);
      }

      setLastScrollY(currentScrollY);
    };

    // 節流處理，避免過於頻繁的狀態更新
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll);
    return () => window.removeEventListener('scroll', throttledScroll);
  }, [lastScrollY]);

  // 拖拽功能
  const handleMouseDown = (e: React.MouseEvent) => {
    // 只有在點擊主按鈕且未展開時才允許拖拽
    if (isExpanded) return;

    setIsDragging(true);
    dragRef.current = {
      startX: e.clientX,
      startY: e.clientY,
      startPosX: position.x,
      startPosY: position.y
    };

    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const deltaX = dragRef.current.startX - e.clientX;
    const deltaY = e.clientY - dragRef.current.startY;

    // 計算新位置，確保不超出視窗邊界
    const newX = Math.max(24, Math.min(window.innerWidth - 80, dragRef.current.startPosX + deltaX));
    const newY = Math.max(24, Math.min(window.innerHeight - 80, dragRef.current.startPosY + deltaY));

    setPosition({ x: newX, y: newY });
  };

  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false);

      // 可選：自動吸附到最近的邊角
      // 這裡可以添加吸附邏輯
      const { x, y } = position;
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      // 簡單的邊緣吸附邏輯
      let newX = x;
      let newY = y;

      // 如果靠近右邊緣，吸附到右邊
      if (x < 100) {
        newX = 24;
      } else if (windowWidth - x < 100) {
        newX = windowWidth - 80;
      }

      // 如果靠近底部，吸附到底部
      if (y < 100) {
        newY = 24;
      } else if (windowHeight - y < 100) {
        newY = windowHeight - 80;
      }

      if (newX !== x || newY !== y) {
        setPosition({ x: newX, y: newY });
      }
    }
  };

  // 拖拽事件監聽
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none'; // 防止拖拽時選中文字
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isDragging]);

  // 點擊外部關閉展開菜單
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fabRef.current && !fabRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isExpanded]);

  // 如果正在加載或懸浮球未啟用，不顯示懸浮按鈕
  if (isLoading || !isFloatingButtonEnabled) {
    return null;
  }

  // 計算懸浮球的實際位置
  const fabStyle = {
    right: `${position.x}px`,
    bottom: `${position.y}px`,
    transform: `translateY(${isHidden ? '100px' : '0px'})`,
    transition: isDragging ? 'none' : 'transform 0.3s ease-out',
  };

  const handleMainButtonClick = (e: React.MouseEvent) => {
    // 如果正在拖拽，不觸發展開
    if (isDragging) return;

    setIsExpanded(!isExpanded);
    e.stopPropagation();
  };

  const handleBugReportClick = () => {
    setShowBugReportModal(true);
    setIsExpanded(false);
  };

  const handleAIClick = () => {
    setShowAIModal(true);
    setIsExpanded(false);
  };

  const handleMinimize = () => {
    setIsMinimized(true);
    setIsExpanded(false);
  };

  const handleRestore = () => {
    setIsMinimized(false);
    setIsHidden(false);
  };

  // 計算環形位置 - Apple風格的弧形展開（優化間距）
  const getCircularPosition = (index: number, total: number) => {
    // 使用更大的間距，確保按鈕不重疊且形成優雅的弧形
    // 按鈕尺寸為48px，需要至少60px的間距

    const mobilePositions = [
      { x: -85, y: -25 },  // Bug回報：偏左方向
      { x: -25, y: -85 },  // AI助手：偏上方向
    ];

    const desktopPositions = [
      { x: -100, y: -35 }, // Bug回報：更偏左方向
      { x: -35, y: -100 }, // AI助手：更偏上方向
    ];

    const positions = isMobile ? mobilePositions : desktopPositions;

    // 使用預設的安全位置
    if (index < positions.length) {
      return positions[index];
    }

    // 備用的動態計算
    const radius = isMobile ? 70 : 90;
    const startAngle = Math.PI * 1.125; // 202.5度
    const endAngle = Math.PI * 1.375;   // 247.5度
    const totalAngle = endAngle - startAngle;
    const angleStep = totalAngle / Math.max(total - 1, 1);
    const angle = startAngle + (index * angleStep);

    return {
      x: Math.cos(angle) * radius,
      y: Math.sin(angle) * radius
    };
  };

  const actionButtons = [
    // Bug 回報按鈕 - 只在測試模式下顯示
    ...(isTestMode ? [{
      id: 'bug-report',
      icon: <Bug size={20} />,
      onClick: handleBugReportClick,
      className: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
      title: '回報Bug',
      label: '回報Bug',
      color: 'red'
    }] : []),
    // AI 助手按鈕 - 始終顯示
    {
      id: 'ai-assistant',
      icon: <Bot size={20} />,
      onClick: handleAIClick,
      className: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
      title: 'AI助手',
      label: 'AI助手',
      color: 'blue'
    }
  ];

  return (
    <>
      {/* 背景遮罩 - 展開時顯示 */}
      {isExpanded && !isMinimized && (
        <div
          className="fixed inset-0 bg-black/10 backdrop-blur-sm z-40 transition-all duration-300"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* 最小化狀態 - 邊緣觸發器 */}
      {isMinimized && (
        <div
          className="fixed right-0 top-1/2 transform -translate-y-1/2 z-50 cursor-pointer"
          onClick={handleRestore}
          title="點擊恢復懸浮球"
        >
          <div className="w-4 h-8 fab-minimized-trigger rounded-l-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full opacity-80"></div>
          </div>
        </div>
      )}

      {/* 主懸浮球 */}
      {!isMinimized && (
        <div
          ref={fabRef}
          className={`fixed z-50 ${isMobile ? 'fab-container-mobile' : ''} ${className} ${
            isDragging ? 'fab-dragging' : 'fab-draggable fab-position-transition'
          } ${isHidden ? 'fab-auto-hide' : 'fab-auto-show'}`}
          style={fabStyle}
        >
        {/* 調試功能已移除，避免方格線干擾 */}
        {/* 環形展開的功能按鈕 */}
        {actionButtons.map((button, index) => {
          const position = getCircularPosition(index, actionButtons.length);
          const isVisible = isExpanded;
          const delay = isVisible ? index * 80 : (actionButtons.length - index - 1) * 50;

          return (
            <button
              key={button.id}
              onClick={button.onClick}
              className={`group absolute flex items-center justify-center w-12 h-12 ${button.className} text-white rounded-full shadow-xl fab-apple-hover fab-apple-glass fab-sub-button transition-all duration-700 ease-out transform hover:scale-110 ${
                isVisible
                  ? 'opacity-100 scale-100'
                  : 'opacity-0 scale-0'
              } ${isMobile ? 'fab-sub-button' : ''}`}
              style={{
                transform: isVisible
                  ? `translate(${position.x}px, ${position.y}px) scale(1)`
                  : 'translate(0px, 0px) scale(0)',
                transitionDelay: `${delay}ms`,
                transitionTimingFunction: isVisible
                  ? 'cubic-bezier(0.175, 0.885, 0.32, 1.275)' // Apple風格彈性進入
                  : 'cubic-bezier(0.55, 0.055, 0.675, 0.19)'  // 快速退出
              }}
              title={button.title}
            >
              {/* 按鈕背景光暈 */}
              <div className={`absolute inset-0 rounded-full bg-white transition-all duration-300 fab-glow-pulse ${
                isVisible ? 'opacity-10 scale-100' : 'opacity-0 scale-0'
              }`} />

              {/* 圖標 */}
              <div className={`relative z-10 transition-all duration-400 ${
                isVisible ? 'rotate-0 scale-100' : 'rotate-180 scale-0'
              }`}>
                {button.icon}
              </div>

              {/* 點擊波紋效果 */}
              <div className="absolute inset-0 rounded-full overflow-hidden">
                <div className="absolute inset-0 rounded-full bg-white opacity-0 scale-0 group-active:opacity-30 group-active:scale-100 transition-all duration-200 fab-ripple" />
              </div>

              {/* 標籤提示 */}
              <span className={`absolute right-14 top-1/2 transform -translate-y-1/2 bg-gray-900/90 backdrop-blur-sm text-white text-sm px-3 py-1.5 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap pointer-events-none shadow-lg ${isMobile ? 'fab-tooltip-mobile' : ''}`}>
                {button.label}
                {/* 箭頭指示器 */}
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-l-4 border-l-gray-900/90 border-t-2 border-t-transparent border-b-2 border-b-transparent" />
              </span>
            </button>
          );
        })}

        {/* 最小化按鈕 */}
        {!isExpanded && !isDragging && (
          <button
            onClick={handleMinimize}
            className="fab-minimize-button absolute -top-2 -right-2 w-6 h-6 text-white rounded-full shadow-lg flex items-center justify-center text-xs z-20"
            title="最小化到邊緣"
          >
            <X size={12} />
          </button>
        )}

        {/* 主按鈕 */}
        <button
          onClick={handleMainButtonClick}
          onMouseDown={handleMouseDown}
          className={`relative flex items-center justify-center w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-full shadow-xl fab-main-button fab-apple-hover fab-main-pulse transition-all duration-500 ease-out transform hover:scale-110 active:scale-95 ${
            isExpanded ? 'scale-110' : 'scale-100'
          } ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
          title={isDragging ? '拖拽移動' : '開發者工具'}
        >
          {/* 外圈光暈 */}
          <div className={`absolute -inset-2 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 transition-all duration-700 fab-glow-pulse ${
            isExpanded ? 'scale-100 opacity-30' : 'scale-0 opacity-0'
          }`} />

          {/* 中圈光暈 */}
          <div className={`absolute -inset-1 rounded-full bg-gradient-to-r from-purple-300 to-pink-300 transition-all duration-500 fab-rotate-glow ${
            isExpanded ? 'scale-100 opacity-20' : 'scale-0 opacity-0'
          }`} />

          {/* 按鈕主體 */}
          <div className="relative z-10 w-full h-full flex items-center justify-center rounded-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300">
            {/* 圖標 */}
            <div className={`transition-all duration-400 ease-out ${
              isExpanded ? 'rotate-45 scale-110' : 'rotate-0 scale-100'
            }`}>
              {isExpanded ? <X size={24} /> : <Plus size={24} />}
            </div>

            {/* 點擊波紋效果 */}
            <div className="absolute inset-0 rounded-full overflow-hidden">
              <div className="absolute inset-0 rounded-full bg-white opacity-0 scale-0 group-active:opacity-30 group-active:scale-100 transition-all duration-200 fab-ripple" />
            </div>
          </div>

          {/* 旋轉的裝飾環 */}
          <div className={`fab-decorator-ring absolute inset-0 rounded-full transition-all duration-1000 ${
            isExpanded ? 'rotate-180 scale-125' : 'rotate-0 scale-100'
          }`} />

          {/* 展開指示器 - 僅在未展開時顯示 */}
          {!isExpanded && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full fab-expand-indicator opacity-80" />
          )}
        </button>
        </div>
      )}

      {/* Bug回報彈窗 - 只在測試模式下顯示 */}
      {showBugReportModal && isTestMode && (
        <BugReportModal
          isOpen={showBugReportModal}
          onClose={() => setShowBugReportModal(false)}
        />
      )}

      {/* AI助手彈窗 */}
      {showAIModal && (
        <AIAssistantModal
          isOpen={showAIModal}
          onClose={() => setShowAIModal(false)}
        />
      )}
    </>
  );
};
