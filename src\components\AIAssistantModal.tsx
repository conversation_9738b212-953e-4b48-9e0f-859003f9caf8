import React from 'react';
import { X, Bo<PERSON>, Sparkles } from 'lucide-react';

interface AIAssistantModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AIAssistantModal: React.FC<AIAssistantModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* 標題欄 */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <Bot className="text-blue-500" size={20} />
            <h2 className="text-lg font-semibold text-gray-900">AI助手</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* 內容 */}
        <div className="p-6 text-center">
          <div className="flex justify-center mb-4">
            <div className="relative">
              <Bot size={48} className="text-blue-500" />
              <Sparkles size={20} className="absolute -top-1 -right-1 text-yellow-500" />
            </div>
          </div>
          
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            AI助手功能開發中
          </h3>
          
          <p className="text-gray-600 mb-6">
            我們正在開發智能助手功能，將為您提供：
          </p>
          
          <div className="text-left space-y-2 mb-6">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-700">智能問答與技術支援</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-700">系統操作指導</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-700">問題診斷與解決方案</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-700">自動化任務建議</span>
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-blue-800">
              <strong>即將推出：</strong> 敬請期待我們的AI助手功能，它將大大提升您的使用體驗！
            </p>
          </div>
          
          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            了解了
          </button>
        </div>
      </div>
    </div>
  );
};
