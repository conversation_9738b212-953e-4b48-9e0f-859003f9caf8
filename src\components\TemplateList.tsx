import React, { useState, useEffect, useRef } from 'react';
import { useTemplateStore } from '../store';
import { Copy, Trash2, Edit, Check, ChevronLeft, ChevronRight, MoreHorizontal, Download, Save, Search, X, Plus } from 'lucide-react';
import { saveTemplateToServer } from '../utils/api/templateApi';
import { MoreMenu } from './ui/MoreMenu';
import { templateMenuItems } from '../services/templateMenuService';
import { buildEndpointUrl } from '../utils/api/apiConfig';
import { useTranslation } from 'react-i18next';
import ColorTypeGradient from './ui/ColorTypeGradient';
import { shortCodeToDisplayColorType, getAllDisplayColorTypes, DisplayColorType } from '../types';
import { getScreenConfigs } from '../screens/screenSizeMap';
import { subscribeToTemplateUpdate, TemplateUpdateEvent } from '../utils/websocketClient';

// 轉換模板的 colorType 為 ColorTypeGradient 組件可識別的格式
const normalizeTemplateColorType = (colorType: string): string => {
  if (!colorType) return 'UNKNOWN';

  // 如果是簡短代碼，轉換為完整描述
  const fullDescription = shortCodeToDisplayColorType(colorType);
  if (fullDescription !== 'UNKNOWN') {
    return fullDescription;
  }

  // 如果已經是完整描述或其他格式，直接返回
  return colorType;
};

// 模板預覽組件
const TemplatePreview: React.FC<{ template: any }> = ({ template }) => {
  // 所有卡片使用固定的外部容器大小
  const containerAspectRatio = '4 / 3'; // 固定所有卡片的寬高比

  // 內部顯示區域根據方向調整
  const innerAspectRatio = template.orientation === 'portrait' ? '9 / 16' : '16 / 9';

  // 判斷是否有預覽圖
  const hasPreviewImage = template.previewImage && typeof template.previewImage === 'string';

  return (
    <div
      className="bg-card border rounded overflow-hidden flex justify-center items-center"
      style={{
        aspectRatio: containerAspectRatio,
        position: 'relative',
      }}
    >
      {/* 如果有預覽圖，則顯示預覽圖 */}
      {hasPreviewImage ? (
        <div
          style={{
            aspectRatio: innerAspectRatio,
            height: template.orientation === 'portrait' ? '90%' : '75%',
            width: template.orientation === 'portrait' ? '56.25%' : '90%',
            position: 'relative',
            backgroundColor: '#f9f9f9',
            border: '1px solid #e1e1e1',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden'
          }}
        >
          <img
            src={template.previewImage}
            alt={template.name}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain'
            }}
          />
        </div>
      ) : (
        /* 內部預覽區域，根據方向調整 - 沒有預覽圖時使用舊的顯示方式 */
        <div
          style={{
            aspectRatio: innerAspectRatio,
            height: template.orientation === 'portrait' ? '90%' : '75%', // 根據方向調整填充效果
            width: template.orientation === 'portrait' ? '56.25%' : '90%',
            position: 'relative',
            backgroundColor: '#f9f9f9',
            border: '1px solid #e1e1e1',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          {/* 顯示模板名稱作為預覽內容的佔位符 */}
          <div className="text-center p-2">
            <div className="font-medium text-sm">{template.name}</div>
            <div className="text-xs text-muted-foreground">{template.screenSize}</div>
            <div className="text-xs text-muted-foreground/70 mt-1">{template.color}</div>
          </div>

          {/* 渲染模板元素的示例 - 修復元件位置計算問題 */}
          {template.elements && template.elements.map((element: any) => {
            // 獲取模板尺寸用於位置計算
            let templateWidth = 250;  // 默認值
            let templateHeight = 122; // 默認值

            if (template.screenSize) {
              const sizeMatch = template.screenSize.match(/(\d+)x(\d+)/);
              if (sizeMatch && sizeMatch[1] && sizeMatch[2]) {
                templateWidth = parseInt(sizeMatch[1], 10);
                templateHeight = parseInt(sizeMatch[2], 10);
              }
            }

            // 計算元件在預覽區域中的正確位置（百分比）
            const leftPercent = (element.x / templateWidth) * 100;
            const topPercent = (element.y / templateHeight) * 100;
            const widthPercent = (Math.abs(element.width) / templateWidth) * 100;
            const heightPercent = (Math.abs(element.height) / templateHeight) * 100;

            if (element.type === 'text' || element.type === 'multiline-text') {
              return (
                <div
                  key={element.id}
                  style={{
                    position: 'absolute',
                    left: `${Math.min(leftPercent, 100 - widthPercent)}%`,
                    top: `${Math.min(topPercent, 100 - heightPercent)}%`,
                    width: `${widthPercent}%`,
                    height: `${heightPercent}%`,
                    fontSize: element.fontSize ? `${Math.max(element.fontSize * 0.3, 8)}px` : '8px', // 縮放字體大小
                    fontFamily: element.fontFamily || 'sans-serif',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: element.type === 'text' ? 'nowrap' : 'normal',
                    display: element.type === 'multiline-text' ? 'flex' : 'block',
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    justifyContent: element.type === 'multiline-text' ? 'flex-start' : 'center',
                    verticalAlign: 'top',
                    color: '#333',
                    lineHeight: '1.2'
                  }}
                >
                  {element.content || '文本內容'}
                </div>
              );
            }

            if (element.type === 'rectangle' || element.type === 'square') {
              return (
                <div
                  key={element.id}
                  style={{
                    position: 'absolute',
                    left: `${leftPercent}%`,
                    top: `${topPercent}%`,
                    width: `${widthPercent}%`,
                    height: `${heightPercent}%`,
                    border: '1px solid black',
                    backgroundColor: element.backgroundColor || 'transparent'
                  }}
                />
              );
            }

            if (element.type === 'icon') {
              // 計算icon的實際大小（容器最小邊長的80%）
              const containerSize = Math.min(Math.abs(element.width), Math.abs(element.height));
              const iconSize = containerSize * 0.8;

              // 計算icon在容器中的居中位置
              const iconOffsetX = (Math.abs(element.width) - iconSize) / 2;
              const iconOffsetY = (Math.abs(element.height) - iconSize) / 2;

              // 計算icon的實際位置百分比
              const iconLeftPercent = ((element.x + iconOffsetX) / templateWidth) * 100;
              const iconTopPercent = ((element.y + iconOffsetY) / templateHeight) * 100;
              const iconWidthPercent = (iconSize / templateWidth) * 100;
              const iconHeightPercent = (iconSize / templateHeight) * 100;

              return (
                <div
                  key={element.id}
                  style={{
                    position: 'absolute',
                    left: `${iconLeftPercent}%`,
                    top: `${iconTopPercent}%`,
                    width: `${iconWidthPercent}%`,
                    height: `${iconHeightPercent}%`,
                    border: '1px solid black',
                    borderRadius: element.iconType === 'circle' ? '50%' : '0',
                    backgroundColor: 'transparent',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: `${Math.max(iconWidthPercent * 0.1, 0.3)}vw`, // 響應式字體大小
                    color: element.lineColor || '#000'
                  }}
                >
                  {/* 簡化的icon顯示 */}
                  {element.iconType === 'star' && '★'}
                  {element.iconType === 'heart' && '♥'}
                  {element.iconType === 'circle' && '●'}
                  {element.iconType === 'square' && '■'}
                  {element.iconType === 'triangle' && '▲'}
                  {(!element.iconType || !['star', 'heart', 'circle', 'square', 'triangle'].includes(element.iconType)) && '●'}
                </div>
              );
            }

            if (element.type === 'circle') {
              return (
                <div
                  key={element.id}
                  style={{
                    position: 'absolute',
                    left: `${leftPercent}%`,
                    top: `${topPercent}%`,
                    width: `${widthPercent}%`,
                    height: `${heightPercent}%`,
                    border: '1px solid black',
                    borderRadius: '50%',
                    backgroundColor: element.backgroundColor || 'transparent'
                  }}
                />
              );
            }

            if (element.type === 'line') {
              // 對於線條元素，需要特別處理位置和尺寸
              let boxLeft = leftPercent;
              let boxTop = topPercent;
              let boxWidth = widthPercent;
              let boxHeight = heightPercent;

              // 根據線段的方向調整選取框位置
              if (element.width < 0) {
                boxLeft = leftPercent - widthPercent;
              }
              if (element.height < 0) {
                boxTop = topPercent - heightPercent;
              }

              return (
                <div
                  key={element.id}
                  style={{
                    position: 'absolute',
                    left: `${boxLeft}%`,
                    top: `${boxTop}%`,
                    width: `${boxWidth}%`,
                    height: `${boxHeight}%`,
                    overflow: 'visible'
                  }}
                >
                  <svg
                    width="100%"
                    height="100%"
                    style={{ overflow: 'visible' }}
                  >
                    <line
                      x1={element.width < 0 ? '100%' : '0%'}
                      y1={element.height < 0 ? '100%' : '0%'}
                      x2={element.width < 0 ? '0%' : '100%'}
                      y2={element.height < 0 ? '0%' : '100%'}
                      stroke="black"
                      strokeWidth="1"
                    />
                  </svg>
                </div>
              );
            }

            // 其他元素類型可以根據需要添加
            return null;
          })}
        </div>
      )}
    </div>
  );
};

// 更新接口定義
interface TemplateListProps {
  onAddTemplate?: () => void;
  store?: { id: string; name: string } | null; // 當前選中的門店
  defaultTemplateTypeFilter?: 'all' | 'system' | 'store'; // 默認的模板類型過濾器設置
  systemTemplatesOnly?: boolean; // 是否只顯示系統模板
}

export const TemplateList: React.FC<TemplateListProps> = ({
  onAddTemplate,
  store,
  defaultTemplateTypeFilter,
  systemTemplatesOnly = false
}) => {
  // 根據是否有門店來決定預設過濾器
  const getDefaultFilter = (): 'all' | 'system' | 'store' => {
    if (systemTemplatesOnly) return 'system';
    if (defaultTemplateTypeFilter) return defaultTemplateTypeFilter;
    // 如果有門店，預設顯示門店模板；如果沒有門店，預設顯示全部
    return store ? 'store' : 'all';
  };
  const { t } = useTranslation();
  const {
    templates,
    deleteTemplate,
    setSelectedTemplate,
    selectedTemplateIds,
    toggleTemplateSelection,
    selectAllTemplates,
    deleteSelectedTemplates,
    addTemplate,
    updateTemplate
  } = useTemplateStore();

  const [selectMode, setSelectMode] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 搜尋相關狀態
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTemplates, setFilteredTemplates] = useState<any[]>([]);

  // 模板類型過濾狀態
  const [templateTypeFilter, setTemplateTypeFilter] = useState<'all' | 'system' | 'store'>(
    getDefaultFilter()
  );

  // 顏色類型過濾狀態
  const [colorTypeFilter, setColorTypeFilter] = useState<string>('all');

  // 尺寸過濾狀態
  const [sizeFilter, setSizeFilter] = useState<string>('all');

  // 分頁相關狀態
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6); // 修改為每頁預設顯示3個項目
  const [jumpToPage, setJumpToPage] = useState('');

  // 記錄是否已經從伺服器載入過模板
  const hasLoadedTemplatesRef = useRef(false);

  // 即時更新相關狀態
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);



  // 獲取所有可用的顏色類型選項
  const getAvailableColorTypes = (): { value: string; label: string }[] => {
    const allColorTypes = getAllDisplayColorTypes();
    return [
      { value: 'all', label: t('templates.allColors') },
      ...allColorTypes.map(colorType => ({
        value: colorType,
        label: colorType
      }))
    ];
  };

  // 獲取所有可用的尺寸選項
  const getAvailableSizes = (): { value: string; label: string }[] => {
    const screenConfigs = getScreenConfigs();
    return [
      { value: 'all', label: t('templates.allSizes') },
      ...screenConfigs.map(config => ({
        value: config.name,
        label: config.name
      }))
    ];
  };

  // 檢查模板尺寸是否匹配選擇的過濾器尺寸
  const isTemplateSizeMatch = (templateScreenSize: string, filterSize: string): boolean => {
    if (!templateScreenSize || filterSize === 'all') return true;

    // 獲取屏幕配置
    const screenConfigs = getScreenConfigs();
    const selectedConfig = screenConfigs.find(config => config.name === filterSize);

    if (!selectedConfig) {
      console.warn(`找不到尺寸配置: ${filterSize}`);
      return false;
    }

    // 可能的匹配格式：
    // 1. 直接匹配尺寸名稱：如 "2.9\""
    // 2. 匹配displayName：如 "2.9\" (128x296)"
    // 3. 匹配分辨率：如 "128x296"
    // 4. 匹配旋轉後的分辨率：如 "296x128"

    const targetResolution = `${selectedConfig.width}x${selectedConfig.height}`;
    const rotatedResolution = `${selectedConfig.height}x${selectedConfig.width}`;

    const isMatch = templateScreenSize === selectedConfig.name ||
                   templateScreenSize === selectedConfig.displayName ||
                   templateScreenSize === targetResolution ||
                   templateScreenSize === rotatedResolution;

    // 可選：調試信息（生產環境可移除）
    // if (filterSize !== 'all') {
    //   console.log(`尺寸匹配檢查: 模板尺寸="${templateScreenSize}", 過濾器="${filterSize}", 匹配=${isMatch}`);
    // }

    return isMatch;
  };

  // 格式化尺寸顯示為"尺寸(解析度)"格式
  const formatScreenSizeDisplay = (templateScreenSize: string): string => {
    if (!templateScreenSize) return '';

    // 獲取屏幕配置
    const screenConfigs = getScreenConfigs();

    // 嘗試匹配屏幕配置
    for (const config of screenConfigs) {
      const targetResolution = `${config.width}x${config.height}`;
      const rotatedResolution = `${config.height}x${config.width}`;

      // 如果模板的screenSize匹配任何已知配置
      if (templateScreenSize === config.name ||
          templateScreenSize === config.displayName ||
          templateScreenSize === targetResolution ||
          templateScreenSize === rotatedResolution) {
        return `${config.name}(${targetResolution})`;
      }
    }

    // 如果沒有匹配到配置，檢查是否已經是"尺寸(解析度)"格式
    if (templateScreenSize.includes('(') && templateScreenSize.includes(')')) {
      return templateScreenSize;
    }

    // 如果是純分辨率格式，嘗試反向查找尺寸
    for (const config of screenConfigs) {
      const targetResolution = `${config.width}x${config.height}`;
      const rotatedResolution = `${config.height}x${config.width}`;

      if (templateScreenSize === targetResolution || templateScreenSize === rotatedResolution) {
        return `${config.name}(${targetResolution})`;
      }
    }

    // 如果都沒匹配到，返回原始值
    return templateScreenSize;
  };

  // 當組件加載時自動從服務器載入模板，但只載入一次
  useEffect(() => {
    // 利用 ref 確保只載入一次，即使在嚴格模式下也不會重複執行
    if (!hasLoadedTemplatesRef.current) {
      console.log('首次加載模板，設置加載標記');
      hasLoadedTemplatesRef.current = true;
      loadTemplatesFromServer();
    }
  }, []); // 空依賴數組，確保只在組件掛載時執行一次

  // 模板即時更新Hook
  useEffect(() => {
    if (!isRealTimeEnabled) return;

    console.log(`啟用模板即時更新: storeId=${store?.id || 'all'}`);

    // 處理模板更新事件（保持選取狀態）
    const handleTemplateUpdate = (event: TemplateUpdateEvent) => {
      // 檢查是否與當前門店相關
      if (event.storeId && store?.id && event.storeId !== store.id) return;

      console.log(`收到模板更新: ${event.templates.length} 個模板`);

      // 根據更新類型處理
      if (event.updateType === 'delete') {
        // 刪除操作：從本地狀態中移除
        const deletedIds = event.templates.map(template => template.id);
        deletedIds.forEach(id => deleteTemplate(id));

        // 清理選取狀態中被刪除的項目
        const currentSelectedIds = [...selectedTemplateIds];
        const validSelectedIds = currentSelectedIds.filter(id => !deletedIds.includes(id));
        if (validSelectedIds.length !== currentSelectedIds.length) {
          selectAllTemplates(false);
          if (validSelectedIds.length > 0) {
            selectAllTemplates(true, validSelectedIds);
          }
        }
      } else if (event.updateType === 'create') {
        // 新增操作：只重新獲取數據，不需要特殊處理
        console.log(`收到模板新增事件: ${event.templates.length} 個新模板`);
        loadTemplatesFromServerWithSelectionPreservation();
      } else if (event.updateType === 'update') {
        // 更新操作：重新獲取數據並保持選取狀態
        console.log(`收到模板更新事件: ${event.templates.length} 個模板`);
        loadTemplatesFromServerWithSelectionPreservation();
      } else {
        // 其他操作類型：重新獲取數據並保持選取狀態
        console.log(`收到模板事件 (${event.updateType}): ${event.templates.length} 個模板`);
        loadTemplatesFromServerWithSelectionPreservation();
      }
    };

    // 訂閱模板更新
    const unsubscribe = subscribeToTemplateUpdate(
      store?.id,
      handleTemplateUpdate
    );

    return unsubscribe;
  }, [isRealTimeEnabled, store?.id]);

  // 搜尋和類型過濾邏輯
  useEffect(() => {
    // 如果設置了只顯示系統模板，強制使用系統模板過濾器
    if (systemTemplatesOnly && templateTypeFilter !== 'system') {
      setTemplateTypeFilter('system');
    }

    // 先確保只顯示當前門店的模板和系統模板
    // 這是為了確保即使選擇「所有模板」，也不會顯示其他門店的模板
    let currentStoreAndSystemTemplates = templates;

    if (systemTemplatesOnly) {
      // 如果是系統模板頁面，只顯示系統模板
      currentStoreAndSystemTemplates = templates.filter(template => template.isSystemTemplate);
    } else {
      // 原有的過濾邏輯
      currentStoreAndSystemTemplates = templates.filter(template =>
        template.isSystemTemplate ||
        (template.storeId && store && template.storeId === store.id) ||
        (!template.storeId && !template.isSystemTemplate) // 舊版模板（沒有storeId和isSystemTemplate的模板）
      );
    }

    // 再按模板類型過濾
    let typeFiltered = currentStoreAndSystemTemplates;

    if (templateTypeFilter === 'system') {
      // 只顯示系統模板
      typeFiltered = currentStoreAndSystemTemplates.filter(template => template.isSystemTemplate);
    } else if (templateTypeFilter === 'store') {
      // 只顯示當前門店的模板
      typeFiltered = currentStoreAndSystemTemplates.filter(template =>
        (template.storeId && !template.isSystemTemplate) ||
        (!template.storeId && !template.isSystemTemplate) // 舊版模板也算作門店模板
      );
    }

    // 按顏色類型過濾
    let colorFiltered = typeFiltered;
    if (colorTypeFilter !== 'all') {
      colorFiltered = typeFiltered.filter(template => {
        if (!template.color) return false;
        // 標準化模板的顏色類型進行比較
        const normalizedTemplateColor = normalizeTemplateColorType(template.color);
        return normalizedTemplateColor === colorTypeFilter;
      });
    }

    // 按尺寸過濾
    let sizeFiltered = colorFiltered;
    if (sizeFilter !== 'all') {
      sizeFiltered = colorFiltered.filter(template => {
        return isTemplateSizeMatch(template.screenSize, sizeFilter);
      });
    }

    // 再按搜尋詞過濾
    if (searchTerm.trim() === '') {
      setFilteredTemplates(sizeFiltered);
    } else {
      const lowercaseSearchTerm = searchTerm.toLowerCase();
      const filtered = sizeFiltered.filter(template =>
        template.name?.toLowerCase().includes(lowercaseSearchTerm) ||
        template.screenSize?.toLowerCase().includes(lowercaseSearchTerm) ||
        template.color?.toLowerCase().includes(lowercaseSearchTerm)
      );
      setFilteredTemplates(filtered);
    }

    // 過濾後重置到第一頁
    setCurrentPage(1);
  }, [searchTerm, templates, templateTypeFilter, colorTypeFilter, sizeFilter, store, systemTemplatesOnly]);

  // 計算總頁數 (使用過濾後的模板數量)
  const totalPages = Math.ceil(filteredTemplates.length / itemsPerPage);

  // 獲取當前頁的模板 (從過濾後的模板列表中)
  const currentTemplates = filteredTemplates.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // 從服務器加載模板
  const loadTemplatesFromServer = async () => {
    try {
      setIsLoading(true);

      // 獲取模板列表前先清理本地緩存的ID集合
      const processedTemplateIds = new Set<string>();

      // 記錄本地已有的模板ID
      templates.forEach(template => {
        processedTemplateIds.add(template.id);
      });

      console.log(`開始從服務器載入模板，本地已有 ${processedTemplateIds.size} 個模板`);

      // 構建API URL，如果有門店ID則添加到查詢參數
      let apiUrl = buildEndpointUrl('templates');
      if (store?.id) {
        apiUrl += `?storeId=${encodeURIComponent(store.id)}`;
        console.log(`按門店ID過濾模板: ${store.id}`);
      }

      // 獲取模板摘要列表
      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`服務器錯誤: ${response.status}`);
      }

      const templatesList = await response.json();
      console.log(`服務器上有 ${templatesList.length} 個模板`);

      // 創建一個新的臨時數組來存儲要添加的模板
      const templatesForAddition: any[] = [];
      const templatesForUpdate: any[] = [];

      // 對於每個模板摘要，獲取完整模板
      for (const templateSummary of templatesList) {
        // 跳過ID為空的無效模板
        if (!templateSummary.id) {
          console.warn(`發現無效的模板摘要，缺少ID`);
          continue;
        }

        // 檢查是否已處理過相同ID的模板
        if (processedTemplateIds.has(templateSummary.id)) {
          console.log(`跳過重複的模板ID: ${templateSummary.id}`);
          continue;
        }

        // 獲取完整模板
        try {
          const detailResponse = await fetch(buildEndpointUrl('templates', templateSummary.id));

          if (!detailResponse.ok) {
            console.error(`無法獲取模板詳情: ${templateSummary.id}，狀態碼: ${detailResponse.status}`);
            continue;
          }

          const fullTemplate = await detailResponse.json();

          // 標記此ID已被處理
          processedTemplateIds.add(fullTemplate.id);

          // 檢查是否已存在相同ID的本地模板
          const existingTemplateIndex = templates.findIndex(t => t.id === fullTemplate.id);

          if (existingTemplateIndex >= 0) {
            // 如果存在相同ID，則更新
            console.log(`更新現有模板: ${fullTemplate.id} - ${fullTemplate.name}`);
            templatesForUpdate.push(fullTemplate);
          } else {
            // 否則添加到待添加列表
            console.log(`添加新模板: ${fullTemplate.id} - ${fullTemplate.name}`);
            templatesForAddition.push(fullTemplate);
          }
        } catch (error) {
          console.error(`獲取模板 ${templateSummary.id} 詳情時出錯:`, error);
        }
      }

      // 批量更新現有模板
      if (templatesForUpdate.length > 0) {
        console.log(`批量更新 ${templatesForUpdate.length} 個現有模板`);
        templatesForUpdate.forEach(template => {
          updateTemplate(template);
        });
      }

      // 批量添加新模板
      if (templatesForAddition.length > 0) {
        console.log(`批量添加 ${templatesForAddition.length} 個新模板`);
        templatesForAddition.forEach(template => {
          addTemplate(template);
        });
      }

      console.log(`從服務器載入模板處理完成: 新增 ${templatesForAddition.length} 個, 更新 ${templatesForUpdate.length} 個`);
    } catch (error) {
      console.error('從服務器加載模板失敗:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 帶選取狀態保持的模板載入函數
  const loadTemplatesFromServerWithSelectionPreservation = async () => {
    try {
      // 記錄當前選取的模板ID
      const currentSelectedIds = [...selectedTemplateIds];

      // 獲取模板列表前先清理本地緩存的ID集合
      const processedTemplateIds = new Set<string>();

      // 記錄本地已有的模板ID
      templates.forEach(template => {
        processedTemplateIds.add(template.id);
      });

      console.log(`[模板自動刷新] 開始載入，本地已有 ${processedTemplateIds.size} 個模板，已選取 ${currentSelectedIds.length} 個`);

      // 構建API URL，如果有門店ID則添加到查詢參數
      let apiUrl = buildEndpointUrl('templates');
      if (store?.id) {
        apiUrl += `?storeId=${encodeURIComponent(store.id)}`;
      }

      // 獲取模板摘要列表
      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(`服務器錯誤: ${response.status}`);
      }

      const templatesList = await response.json();

      // 創建一個新的臨時數組來存儲要添加的模板
      const templatesForAddition: any[] = [];
      const templatesForUpdate: any[] = [];

      // 對於每個模板摘要，獲取完整模板
      for (const templateSummary of templatesList) {
        // 跳過ID為空的無效模板
        if (!templateSummary.id) continue;

        // 檢查是否已處理過相同ID的模板
        if (processedTemplateIds.has(templateSummary.id)) continue;

        // 獲取完整模板
        try {
          const detailResponse = await fetch(buildEndpointUrl('templates', templateSummary.id));
          if (!detailResponse.ok) continue;

          const fullTemplate = await detailResponse.json();
          processedTemplateIds.add(fullTemplate.id);

          // 檢查是否已存在相同ID的本地模板
          const existingTemplateIndex = templates.findIndex(t => t.id === fullTemplate.id);

          if (existingTemplateIndex >= 0) {
            templatesForUpdate.push(fullTemplate);
          } else {
            templatesForAddition.push(fullTemplate);
          }
        } catch (error) {
          console.error(`獲取模板 ${templateSummary.id} 詳情時出錯:`, error);
        }
      }

      // 批量更新現有模板
      if (templatesForUpdate.length > 0) {
        templatesForUpdate.forEach(template => {
          updateTemplate(template);
        });
      }

      // 批量添加新模板
      if (templatesForAddition.length > 0) {
        templatesForAddition.forEach(template => {
          addTemplate(template);
        });
      }

      // 檢查選取項目是否還存在，保持有效的選取狀態
      if (currentSelectedIds.length > 0) {
        const existingIds = templates.map(t => t.id).filter(Boolean);
        const validSelectedIds = currentSelectedIds.filter(id => existingIds.includes(id));

        if (validSelectedIds.length !== currentSelectedIds.length) {
          console.log(`[模板選取保持] 清理無效選取項目: ${currentSelectedIds.length} -> ${validSelectedIds.length}`);
          // 清空所有選取，然後重新選取有效的項目
          selectAllTemplates(false);
          if (validSelectedIds.length > 0) {
            selectAllTemplates(true, validSelectedIds);
          }
        }
      }

      // 保持分頁狀態 - 檢查當前頁面是否仍然有效
      const newTotalPages = Math.ceil(templates.length / itemsPerPage);
      if (currentPage > newTotalPages && newTotalPages > 0) {
        // 如果當前頁面超出了新的總頁數，調整到最後一頁
        console.log(`[模板分頁保持] 當前頁面 ${currentPage} 超出新總頁數 ${newTotalPages}，調整到最後一頁`);
        setCurrentPage(newTotalPages);
      }
      // 如果當前頁面仍然有效，則保持不變

      console.log(`[模板自動刷新] 處理完成: 新增 ${templatesForAddition.length} 個, 更新 ${templatesForUpdate.length} 個`);
    } catch (error) {
      console.error('[模板自動刷新] 失敗:', error);
      // 自動刷新失敗時不顯示錯誤，避免干擾用戶
    }
  };

  // 頁面切換函數
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // 處理跳頁操作
  const handleJumpToPage = () => {
    const pageNumber = parseInt(jumpToPage);
    if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setJumpToPage('');
    } else {
      alert(`請輸入有效的頁碼 (1-${totalPages})`);
    }
  };

  // 當模板數量變化或每頁顯示數量變化時重置當前頁
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1);
    }
  }, [templates.length, itemsPerPage, totalPages, currentPage]);

  // 處理只選擇搜尋結果中的所有項目
  const handleSelectAllFiltered = (checked: boolean) => {
    if (checked) {
      // 如果是勾選，則只選擇過濾後的模板
      const filteredIds = filteredTemplates.map(template => template.id);
      selectAllTemplates(true, filteredIds);
    } else {
      // 如果是取消選擇，則清空所有選擇
      selectAllTemplates(false);
    }
  };

  // 計算是否所有過濾後的模板都被選中
  const allFilteredSelected =
    filteredTemplates.length > 0 &&
    filteredTemplates.every(template => selectedTemplateIds.includes(template.id));

  // 切換選擇模式
  const toggleSelectMode = () => {
    if (selectMode) {
      // 退出選擇模式時，清空所有選擇
      selectAllTemplates(false);
    }
    setSelectMode(!selectMode);
  };

  // 處理刪除模板
  const handleDeleteTemplate = async (id: string, name: string) => {
    try {
      if (window.confirm(t('templates.confirmDeleteTemplate', { name }))) {
        // 先顯示刪除中的提示
        console.log(`正在刪除模板: ${id}`);        // 向後端發送刪除請求
        const response = await fetch(buildEndpointUrl('templates', id), {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error(`服務器錯誤: ${response.status}`);
        }

        // 成功後從本地狀態中刪除
        deleteTemplate(id);
        console.log(`模板 ${id} 刪除成功`);
      }
    } catch (error) {
      console.error('刪除模板失敗:', error);
      alert('刪除模板失敗，請稍後再試');
    }
  };

  // 處理批量刪除
  const handleBulkDelete = async () => {
    if (window.confirm(t('templates.confirmDeleteSelected', { count: selectedTemplateIds.length }))) {
      try {
        // 顯示刪除中的提示
        console.log(`正在刪除 ${selectedTemplateIds.length} 個模板`);        // 並行發送所有刪除請求
        const deletePromises = selectedTemplateIds.map(id =>
          fetch(buildEndpointUrl('templates', id), {
            method: 'DELETE'
          })
        );

        await Promise.all(deletePromises);

        // 成功後從本地狀態中刪除
        deleteSelectedTemplates();
        console.log('批量刪除成功');
      } catch (error) {
        console.error('批量刪除失敗:', error);
        alert('批量刪除失敗，請稍後再試');
      }
    }
  };

  // 處理打開更多菜單
  const handleToggleMoreMenu = (templateId: string) => {
    setShowMoreMenu(showMoreMenu === templateId ? null : templateId);
  };

  // 處理點擊其他地方關閉菜單
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.more-menu') && !target.closest('.more-button')) {
        setShowMoreMenu(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // 匯出模板為 JSON 文件
  const handleExportTemplate = async (template: any) => {
    try {
      // 關閉更多菜單
      setShowMoreMenu(null);

      // 創建 JSON 字符串
      const templateJson = JSON.stringify(template, null, 2);

      // 創建 Blob 對象
      const blob = new Blob([templateJson], { type: 'application/json' });

      // 創建下載鏈接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name || 'template'}.json`;

      // 觸發下載
      document.body.appendChild(a);
      a.click();

      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('匯出模板失敗:', error);
      alert('匯出模板失敗');
    }
  };

  // 處理搜尋框清除
  const handleClearSearch = () => {
    setSearchTerm('');
  };

  return (
    <>
      {/* Header 區塊 */}
      <header className="bg-muted shadow sticky top-0 z-20 -mx-2 sm:-mx-6 lg:-mx-4 px-2 sm:px-6 lg:px-4 mb-4">
        <div className="max-w-7xl mx-auto py-2">
          <div className="flex justify-between items-center">
            <div className="flex gap-4">
              {/* 如果不是只顯示系統模板，才顯示過濾下拉選單 */}
              {!systemTemplatesOnly && (
                <select
                  className="border rounded-md px-1.5 py-1.5"
                  value={templateTypeFilter}
                  onChange={(e) => setTemplateTypeFilter(e.target.value as 'all' | 'system' | 'store')}
                >
                  <option value="all">{t('templates.allTemplates')}</option>
                  <option value="system">{t('templates.systemTemplate')}</option>
                  <option value="store">{t('templates.storeTemplate')}</option>
                </select>
              )}
              <select
                className="border rounded-md px-1.5 py-1.5"
                value={sizeFilter}
                onChange={(e) => setSizeFilter(e.target.value)}
              >
                {getAvailableSizes().map(size => (
                  <option key={size.value} value={size.value}>
                    {size.label}
                  </option>
                ))}
              </select>
              <select
                className="border rounded-md px-1.5 py-1.5"
                value={colorTypeFilter}
                onChange={(e) => setColorTypeFilter(e.target.value)}
              >
                {getAvailableColorTypes().map(colorType => (
                  <option key={colorType.value} value={colorType.value}>
                    {colorType.label}
                  </option>
                ))}
              </select>
              {/* 搜尋框 */}
              <div className="relative flex-grow max-w-md px-1.5 ">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={t('templates.searchPlaceholder')}
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {searchTerm && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute inset-y-0 right-0 flex items-center pr-3"
                    title={t('common.clearSearch')}
                  >
                    <X size={16} className="text-gray-400 hover:text-gray-600" />
                  </button>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={onAddTemplate}
                className="flex items-center gap-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              >
                <Plus size={18} />
                {t('common.add')}
              </button>
              <MoreMenu menuItems={templateMenuItems} buttonStyle="green" />
            </div>
          </div>
        </div>
      </header>

      <div className="space-y-2">
        {/* 工具列和搜尋框 */}
        <div className="flex flex-wrap gap-2 justify-between items-center bg-card p-3 rounded-lg shadow-sm">
          <div className="flex items-center space-x-2">
            {selectMode && (
              <>
                <input
                  type="checkbox"
                  checked={allFilteredSelected}
                  onChange={(e) => handleSelectAllFiltered(e.target.checked)}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
                <span className="text-sm">
                  {t('templates.selectedCount', { selected: selectedTemplateIds.length, total: filteredTemplates.length })}
                </span>
              </>
            )}
            {isLoading && (
              <div className="text-sm text-muted-foreground">{t('common.loading')}</div>
            )}

            {/* 即時更新狀態指示器 */}
            <div className={`flex items-center gap-2 px-3 py-1.5 rounded-md border ${
              isRealTimeEnabled ? 'bg-green-50 border-green-300' : 'bg-gray-100 border-gray-300'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                isRealTimeEnabled ? 'bg-green-500' : 'bg-gray-400'
              }`} />
              <span className={`text-xs ${
                isRealTimeEnabled ? 'text-green-700' : 'text-gray-500'
              }`}>
                {isRealTimeEnabled ? '即時更新已開啟' : '即時更新已關閉'}
              </span>
              <button
                onClick={() => setIsRealTimeEnabled(!isRealTimeEnabled)}
                className="text-xs text-blue-600 hover:text-blue-800 underline ml-1"
              >
                {isRealTimeEnabled ? '關閉' : '開啟'}
              </button>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {selectMode ? (
              <>
                {selectedTemplateIds.length > 0 && (
                  <button
                    onClick={handleBulkDelete}
                    className="px-3 py-1.5 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                  >
                    {t('templates.deleteSelected')}
                  </button>
                )}
                <button
                  onClick={toggleSelectMode}
                  className="px-3 py-1.5 border border-gray-300 rounded text-sm"
                >
                  {t('templates.cancelSelection')}
                </button>
              </>
            ) : (
              <button
                onClick={toggleSelectMode}
                className="px-3 py-1.5 border border-gray-300 rounded text-sm"
              >
                {t('templates.multiSelectMode')}
              </button>
            )}
          </div>
        </div>

        {/* 搜尋結果提示 */}
        {searchTerm && (
          <div className="bg-blue-50 p-2 rounded flex items-center justify-between">
            <div className="text-sm text-blue-700">
              {t('templates.searchResults', { term: searchTerm, count: filteredTemplates.length })}
            </div>
            <button
              onClick={handleClearSearch}
              className="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              {t('common.clearSearch')}
            </button>
          </div>
        )}

        {/* 模板網格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {currentTemplates.map((template) => (
            <div
              key={template.id}
              className={`
                group relative bg-background rounded-xl
                shadow-lg hover:shadow-2xl
                border border-border hover:border-border/80
                transition-all duration-300 ease-out
                transform hover:-translate-y-1 hover:scale-[1.02]
                ${selectedTemplateIds.includes(template.id)
                  ? 'ring-2 ring-blue-500 shadow-blue-100 dark:shadow-blue-900/20'
                  : ''
                }
              `}
            >
              {/* 高質感光幕效果 */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/0 via-purple-50/0 to-pink-50/0
                             group-hover:from-blue-50/30 group-hover:via-purple-50/20 group-hover:to-pink-50/30
                             transition-all duration-500 ease-out pointer-events-none z-0" />

              {/* 玻璃動態光效 - 左至右掃光 */}
              <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none z-0">
                <div className="absolute inset-0 w-full h-full bg-gradient-to-r
                               from-transparent via-white/40 to-transparent
                               opacity-0 group-hover:opacity-100 transition-all duration-800 ease-out
                               transform -translate-x-full group-hover:translate-x-full
                               scale-x-150" />
              </div>

              {/* 次光束 - 藍色光暈 */}
              <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none z-0">
                <div className="absolute inset-0 w-full h-full bg-gradient-to-r
                               from-transparent via-blue-300/25 to-transparent
                               opacity-0 group-hover:opacity-100 transition-all duration-1000 ease-out
                               transform -translate-x-full group-hover:translate-x-full delay-200
                               scale-x-125" />
              </div>

              {/* 第三層光束 - 紫色光暈 */}
              <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none z-0">
                <div className="absolute inset-0 w-full h-full bg-gradient-to-r
                               from-transparent via-purple-300/20 to-transparent
                               opacity-0 group-hover:opacity-100 transition-all duration-1200 ease-out
                               transform -translate-x-full group-hover:translate-x-full delay-400
                               scale-x-110" />
              </div>

              {/* 邊緣光效 */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/8 to-transparent
                             opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-0" />

              {/* 玻璃反射效果 */}
              <div className="absolute top-0 left-0 right-0 h-1/3 rounded-t-xl
                             bg-gradient-to-b from-white/10 to-transparent
                             opacity-0 group-hover:opacity-100 transition-opacity duration-400 pointer-events-none z-0" />

              {/* 卡片內容容器 */}
              <div className="relative z-10 p-6">
                {/* 選擇 checkbox */}
                {selectMode && (
                  <div className="absolute top-4 right-4 z-20">
                    <div
                      className={`w-6 h-6 flex items-center justify-center rounded-lg border-2 cursor-pointer
                                 transition-all duration-200 backdrop-blur-sm
                                 ${selectedTemplateIds.includes(template.id)
                                   ? 'bg-blue-500 border-blue-500 shadow-lg shadow-blue-200'
                                   : 'bg-card/80 border-border hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                                 }`}
                      onClick={() => toggleTemplateSelection(template.id)}
                    >
                      {selectedTemplateIds.includes(template.id) && (
                        <Check size={16} className="text-white" />
                      )}
                    </div>
                  </div>
                )}

                {/* 模板預覽區域 */}
                <div
                  className="mb-6 cursor-pointer rounded-lg
                           bg-gradient-to-br from-muted/50 to-muted p-3
                           transition-all duration-300 ease-out hover:shadow-md border border-border/50"
                  onClick={() => {
                    if (selectMode) {
                      toggleTemplateSelection(template.id);
                    } else {
                      setSelectedTemplate(template);
                    }
                  }}
                >
                  <TemplatePreview template={template} />
                </div>

                {/* 模板資訊區域 */}
                <div className="space-y-4">
                  {/* 標題和模板來源 - 名稱靠左，來源標籤靠右 */}
                  <div className="flex items-start justify-between">
                    <h3 className="font-semibold text-lg text-foreground group-hover:text-blue-600
                                   transition-colors duration-200 flex-1 mr-2">
                      {template.name}
                    </h3>

                    {/* 模板類型標籤 - 靠右 */}
                    <div className="flex flex-wrap gap-1 justify-end">
                      {template.isSystemTemplate && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                       bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800
                                       border border-blue-200 shadow-sm">
                          {t('templates.systemTemplate')}
                        </span>
                      )}
                      {template.storeId && !template.isSystemTemplate && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                       bg-gradient-to-r from-green-100 to-green-200 text-green-800
                                       border border-green-200 shadow-sm">
                          {store && template.storeId === store.id
                            ? `${t('templates.storeTemplate')} (${store.name})`
                            : t('templates.storeTemplate')}
                        </span>
                      )}
                      {!template.storeId && !template.isSystemTemplate && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                       bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700
                                       border border-gray-200 shadow-sm">
                          {t('templates.legacyTemplate')}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* 解析度和顏色類型 - 解析度靠左，顏色靠右 */}
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground font-medium">{formatScreenSizeDisplay(template.screenSize)}</p>

                    {/* 顏色類型顯示 - 靠右 */}
                    {template.color && (
                      <div className="w-24 h-4 rounded-md overflow-hidden shadow-sm">
                        <ColorTypeGradient colorType={normalizeTemplateColorType(template.color)} size="sm" />
                      </div>
                    )}
                  </div>
                </div>

                {/* 操作按鈕區域 */}
                {!selectMode && (
                  <div className="mt-6 pt-4 border-t border-gray-100">
                    <div className="flex justify-between items-center">
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            console.log('選擇編輯模板:', template.id, template.name);
                            if (!template.id) {
                              console.error('錯誤: 嘗試編輯的模板沒有 ID');
                              alert('無法編輯此模板: 模板 ID 缺失');
                              return;
                            }
                            setSelectedTemplate(template);
                          }}
                          className="relative p-2.5 rounded-lg bg-blue-50 text-blue-600 hover:bg-blue-100
                                   hover:text-blue-700 transition-all duration-200
                                   hover:shadow-md hover:scale-105 group/btn overflow-hidden"
                          title={t('templates.editTemplate')}
                        >
                          <Edit size={18} className="relative z-10 group-hover/btn:scale-110 transition-transform duration-200" />
                          {/* 按鈕多角度光效 - 對角線 */}
                          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/60 to-transparent
                                         opacity-0 group-hover/btn:opacity-100 transition-all duration-400 ease-out
                                         transform -translate-x-full -translate-y-full
                                         group-hover/btn:translate-x-full group-hover/btn:translate-y-full
                                         rotate-12 scale-150" />
                        </button>

                        <button
                          onClick={() => handleDeleteTemplate(template.id, template.name)}
                          className="relative p-2.5 rounded-lg bg-red-50 text-red-600 hover:bg-red-100
                                   hover:text-red-700 transition-all duration-200
                                   hover:shadow-md hover:scale-105 group/btn overflow-hidden"
                          title={t('templates.deleteTemplate')}
                        >
                          <Trash2 size={18} className="relative z-10 group-hover/btn:scale-110 transition-transform duration-200" />
                          {/* 按鈕多角度光效 - 垂直 */}
                          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/60 to-transparent
                                         opacity-0 group-hover/btn:opacity-100 transition-all duration-400 ease-out
                                         transform -translate-y-full group-hover/btn:translate-y-full
                                         scale-y-150" />
                        </button>

                        <button
                          className="relative p-2.5 rounded-lg bg-green-50 text-green-600 hover:bg-green-100
                                   hover:text-green-700 transition-all duration-200
                                   hover:shadow-md hover:scale-105 group/btn overflow-hidden"
                          title={t('templates.copyTemplate')}
                        >
                          <Copy size={18} className="relative z-10 group-hover/btn:scale-110 transition-transform duration-200" />
                          {/* 按鈕多角度光效 - 反對角線 */}
                          <div className="absolute inset-0 bg-gradient-to-tl from-transparent via-white/60 to-transparent
                                         opacity-0 group-hover/btn:opacity-100 transition-all duration-400 ease-out
                                         transform translate-x-full -translate-y-full
                                         group-hover/btn:-translate-x-full group-hover/btn:translate-y-full
                                         -rotate-12 scale-150" />
                        </button>
                      </div>

                      <div className="relative">
                        <button
                          className="relative p-2.5 rounded-lg bg-gray-50 text-gray-600 hover:bg-gray-100
                                   hover:text-gray-700 transition-all duration-200
                                   hover:shadow-md hover:scale-105 group/btn more-button overflow-hidden"
                          title={t('templates.moreOptions')}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleMoreMenu(template.id);
                          }}
                        >
                          <MoreHorizontal size={18} className="relative z-10 group-hover/btn:scale-110 transition-transform duration-200" />
                          {/* 按鈕多角度光效 - 水平 */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent
                                         opacity-0 group-hover/btn:opacity-100 transition-all duration-400 ease-out
                                         transform -translate-x-full group-hover/btn:translate-x-full
                                         scale-x-200" />
                        </button>

                        {showMoreMenu === template.id && (
                          <div className="absolute right-0 bottom-full mb-2 w-48 bg-white rounded-xl shadow-xl
                                        border border-gray-100 z-[9999] more-menu overflow-hidden
                                        backdrop-blur-sm bg-white/95"
                               style={{ zIndex: 9999 }}>
                            <div className="py-2">
                              <button
                                onClick={() => handleExportTemplate(template)}
                                className="flex items-center w-full px-4 py-3 text-sm text-gray-700
                                         hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50
                                         hover:text-blue-700 transition-all duration-200"
                              >
                                <Download size={16} className="mr-3" />
                                {t('templates.exportTemplate')}
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* 當前頁無內容時的填充 */}
          {currentTemplates.length === 0 && filteredTemplates.length > 0 && (
            <div className="col-span-3 bg-white rounded-lg shadow-md p-8 text-center">
              <p className="text-gray-500">{t('templates.noContentOnPage')}</p>
            </div>
          )}

          {/* 搜尋無結果時的提示 */}
          {filteredTemplates.length === 0 && searchTerm && (
            <div className="col-span-3 bg-white rounded-lg shadow-md p-8 text-center">
              <p className="text-gray-500">{t('templates.noMatchingTemplates', { term: searchTerm })}</p>
            </div>
          )}
        </div>

        {/* 沒有模板時顯示的提示 */}
        {templates.length === 0 && (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <p className="text-gray-500">{t('templates.noTemplates')}</p>
          </div>
        )}

        {/* 分頁控制 */}
        {filteredTemplates.length > 0 && (
          <div className="flex flex-wrap items-center justify-between bg-card p-4 rounded-lg shadow-sm">
            <div className="flex items-center space-x-3 mb-2 sm:mb-0">
              <span className="text-sm text-muted-foreground">{t('common.itemsPerPage')}:</span>
              <select
                value={itemsPerPage}
                onChange={(e) => setItemsPerPage(Number(e.target.value))}
                className="border rounded px-2 py-1 text-sm"
              >
                <option value={3}>3</option>
                <option value={6}>6</option>
                <option value={9}>9</option>
                <option value={12}>12</option>
                <option value={15}>15</option>
                <option value={18}>18</option>
                <option value={24}>24</option>
              </select>
              <span className="text-sm text-muted-foreground">
                {t('common.showing')} {filteredTemplates.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0}-
                {Math.min(currentPage * itemsPerPage, filteredTemplates.length)} {t('common.of')} {filteredTemplates.length} {t('common.items')}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => goToPage(1)}
                  disabled={currentPage === 1}
                  className={`px-2.5 py-1.5 border rounded ${currentPage === 1 ? 'text-muted-foreground cursor-not-allowed' : 'hover:bg-muted'
                    }`}
                >
                  {t('common.firstPage')}
                </button>

                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`p-1.5 border rounded ${currentPage === 1 ? 'text-muted-foreground cursor-not-allowed' : 'hover:bg-muted'
                    }`}
                >
                  <ChevronLeft size={16} />
                </button>

                {/* 頁碼按鈕 */}
                <div className="hidden sm:flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, index) => {
                    let pageNum;

                    if (totalPages <= 5) {
                      pageNum = index + 1;
                    } else if (currentPage <= 3) {
                      pageNum = index + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + index;
                    } else {
                      pageNum = currentPage - 2 + index;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => goToPage(pageNum)}
                        className={`min-w-[32px] h-8 flex items-center justify-center border rounded ${currentPage === pageNum
                            ? 'bg-blue-500 text-white border-blue-500'
                            : 'hover:bg-muted'
                          }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`p-1.5 border rounded ${currentPage === totalPages ? 'text-muted-foreground cursor-not-allowed' : 'hover:bg-muted'
                    }`}
                >
                  <ChevronRight size={16} />
                </button>

                <button
                  onClick={() => goToPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className={`px-2.5 py-1.5 border rounded ${currentPage === totalPages ? 'text-muted-foreground cursor-not-allowed' : 'hover:bg-muted'
                    }`}
                >
                  {t('common.lastPage')}
                </button>
              </div>

              <div className="flex items-center space-x-1">
                <span className="text-sm text-muted-foreground">{t('common.jumpTo')}</span>
                <input
                  type="text"
                  value={jumpToPage}
                  onChange={(e) => {
                    // 只允許輸入數字
                    const value = e.target.value.replace(/\D/g, '');
                    setJumpToPage(value);
                  }}
                  className="w-12 border rounded px-2 py-1 text-sm"
                  placeholder={currentPage.toString()}
                />
                <span className="text-sm text-muted-foreground">{t('common.page')}</span>
                <button
                  onClick={handleJumpToPage}
                  className="px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                >
                  {t('common.confirm')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};