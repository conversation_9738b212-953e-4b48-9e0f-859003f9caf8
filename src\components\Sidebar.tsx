import React from 'react';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './ui/LanguageSwitcher';
import { useAuthStore } from '../store/authStore';
import { useNavigationStore } from '../store/navigationStore';
import { PermissionMenuItem } from './permission/PermissionMenuItem';
import { DevModeWrapper } from './DevModeWrapper';
import {
  Menu,
  Building,
  LayoutTemplate,
  Settings,
  Upload,
  BarChart3,
  Users,
  HardDrive,
  Database,
  Store,
  ChevronLeft,
  FileText,
  Shield,
  ClipboardList,
  LogOut,
  Key,
  TestTube,
  Bug
} from 'lucide-react';

type SidebarItem = {
  id: string;
  translationKey: string;
  icon: React.ReactNode;
  permission?: string;
  anyPermissions?: string[];
  allPermissions?: string[];
};

// 第一層選單項目 - 門店管理和系統相關項目
const firstLevelItems: SidebarItem[] = [
  // 門店管理選項 - 允許系統角色的 store:view 權限或任何門店角色權限
  { id: 'store-management', translationKey: 'sidebar.storeManagement', icon: <Building size={20} />, anyPermissions: [
    'store:view',
    'store-data:view', 'store-data:create', 'store-data:update', 'store-data:delete',
    'store-template:view', 'store-template:create', 'store-template:update', 'store-template:delete',
    'gateway:view', 'gateway:create', 'gateway:update', 'gateway:delete',
    'device:view', 'device:create', 'device:update', 'device:delete',
    'store-settings:view', 'store-settings:update',
    'analytics:view', 'analytics:export'
  ] },
  { id: 'system-data', translationKey: 'sidebar.systemData', icon: <Database size={20} />, anyPermissions: ['system:view', 'system-data:view'] },
  { id: 'system-templates', translationKey: 'sidebar.systemTemplates', icon: <LayoutTemplate size={20} />, permission: 'template:view' },
  { id: 'permission-management', translationKey: 'sidebar.permissionManagement', icon: <Shield size={20} />, anyPermissions: ['role:view', 'user:view', 'permission:view'] },
  { id: 'system-logs', translationKey: 'sidebar.systemLogs', icon: <ClipboardList size={20} />, permission: 'system:view' },
  { id: 'settings', translationKey: 'sidebar.systemConfig', icon: <Settings size={20} />, permission: 'system:view' },
  { id: 'change-password', translationKey: 'auth.changePassword', icon: <Key size={20} /> },
  { id: 'color-test', translationKey: 'test.colorRestriction', icon: <TestTube size={20} /> },
  { id: 'bug-management', translationKey: 'sidebar.bugManagement', icon: <Bug size={20} /> },
];

// 第二層選單項目 - 門店管理下的子選單
const secondLevelItems: SidebarItem[] = [
  { id: 'store-overview', translationKey: 'sidebar.storeOverview', icon: <Store size={20} />, anyPermissions: ['store:view', 'store-data:view'] },
  { id: 'database', translationKey: 'sidebar.database', icon: <Database size={20} />, anyPermissions: ['store:view', 'store-data:view'] },
  { id: 'templates', translationKey: 'sidebar.templates', icon: <LayoutTemplate size={20} />, anyPermissions: ['template:view', 'store-template:view'] },
  { id: 'deploy', translationKey: 'sidebar.deploy', icon: <Upload size={20} />, anyPermissions: ['gateway:view'] },
  { id: 'devices', translationKey: 'sidebar.devices', icon: <HardDrive size={20} />, anyPermissions: ['device:view'] },
  { id: 'users', translationKey: 'sidebar.users', icon: <Users size={20} />, anyPermissions: ['store:update', 'store-settings:view', 'store-settings:update'] },
  { id: 'analytics', translationKey: 'sidebar.analytics', icon: <BarChart3 size={20} />, anyPermissions: ['system:view', 'analytics:view'] },
];

interface SidebarProps {
  activeItem: string;
  setActiveItem: (id: string) => void;
  isSidebarCollapsed: boolean;
  toggleSidebar: () => void;
  showSecondLevel: boolean;
  setShowSecondLevel: (show: boolean) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  activeItem,
  setActiveItem,
  isSidebarCollapsed,
  toggleSidebar,
  showSecondLevel,
  setShowSecondLevel
}) => {
  const { t } = useTranslation();
  const { logout, user } = useAuthStore();
  // 使用 navigationStore 來同步導航狀態
  const { setActiveItem: setGlobalActiveItem, setShowSecondLevel: setGlobalShowSecondLevel } = useNavigationStore();

  // 根據層級選擇不同的選單項目
  const currentItems = showSecondLevel ? secondLevelItems : firstLevelItems;

  return (
    <div className={`bg-slate-700 text-white h-screen flex flex-col ${isSidebarCollapsed ? 'w-16' : 'w-58'} transition-all duration-300`}>
      <div className="flex items-center p-4 border-b border-slate-600">
        <button
          onClick={toggleSidebar}
          className="p-1 rounded hover:bg-slate-800"
        >
          <Menu size={24} />
        </button>
        {!isSidebarCollapsed && (
          <h1 className="ml-3 text-xl font-semibold">EPD Manager</h1>
        )}
      </div>

      {/* 返回按鈕，只在第二層選單時顯示 */}
      {showSecondLevel && (
        <div className="p-2 border-b border-slate-600">
          <button
            onClick={() => {
              setShowSecondLevel(false);
              setGlobalShowSecondLevel(false); // 同步到全局狀態
              setActiveItem('store-management');
              setGlobalActiveItem('store-management'); // 同步到全局狀態
            }}
            className="flex items-center w-full p-2 rounded-md hover:bg-slate-600"
          >
            <ChevronLeft size={20} />
            {!isSidebarCollapsed && (
              <span className="ml-3">{t('common.back')}</span>
            )}
          </button>
        </div>
      )}

      <div className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-2 px-2">
          {currentItems.map((item: SidebarItem) => {
            const menuItem = (
              <PermissionMenuItem
                key={item.id}
                permission={item.permission}
                anyPermissions={item.anyPermissions}
                allPermissions={item.allPermissions}
              >
                <li>
                  <button
                    onClick={() => {
                      setActiveItem(item.id);
                      setGlobalActiveItem(item.id); // 同步到全局狀態
                    }}
                    className={`flex items-center ${isSidebarCollapsed ? 'justify-center' : 'justify-start'} w-full p-2 rounded-md ${
                      activeItem === item.id
                        ? 'bg-blue-600 text-white'
                        : 'hover:bg-slate-600'
                    }`}
                  >
                    <span className="flex-shrink-0">{item.icon}</span>
                    {!isSidebarCollapsed && (
                      <span className="ml-3">{t(item.translationKey)}</span>
                    )}
                  </button>
                </li>
              </PermissionMenuItem>
            );

            // 如果是 bug-management 項目，需要用 DevModeWrapper 包裝
            if (item.id === 'bug-management') {
              return (
                <DevModeWrapper key={item.id}>
                  {menuItem}
                </DevModeWrapper>
              );
            }

            return menuItem;
          })}
        </ul>
      </div>

      {!isSidebarCollapsed ? (
        <div className="p-4 border-t border-slate-600 space-y-2">
          <LanguageSwitcher className="w-full" />
        </div>
      ) : (
        <div className="p-2 border-t border-slate-600">
          <LanguageSwitcher className="w-full" />
        </div>
      )}
    </div>
  );
};